.hero {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 8px;
}

.hero-content {
    height: 80vh;
    min-height: 600px;
    max-width: 1440px;
    width: 100%;
    margin: 8px;
    background-size: cover !important;
    background-position: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    position: relative;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%), linear-gradient(0deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), linear-gradient(0deg, #016449, #016449);
    background-blend-mode: normal, normal, color, normal, normal;
    border-radius: 24px;
    padding: 2rem;
}

.banner {
    width: 50vw;
    padding: 2rem;
}

.hero-cta {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .banner {
        width: 100%;
        padding: 0;
    }
}

