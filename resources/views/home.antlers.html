<div class="hero">
    <div class="hero-content"  style="  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%), linear-gradient(0deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), linear-gradient(0deg, #016449, #016449), url('{{ glide:brand:header_img w="1080" h="720" fit="crop" q="80" }}');">
        <div class="banner">
            <h1>Blod, Svett & Barr</h1>
            <span>Vi genomför 99% av alla våra aktiviteter ute i friska luften. Välkomna till OK Tyr, Värmlands största orienterings och cykelklubb med hemmaplan i Karlstad och Hammarö.</span>
            <div class="hero-cta">
                <a class="button button-secondary" href="/kalender">Kalendern</a>
                <a class="button button-primary" href="/bli-medlem">Bli medlem</a>
            </div>
        </div>
    </div>
</div>

<!-- Hero Row Section -->
<section>
    <div>
        <div>
            <!-- Hero News Item -->
            <div>
                {{ collection:news limit="1" }}
                    <article>
                        <a href="{{ url }}">
                            <div>
                                {{ if featured_image }}
                                    <img src="{{ glide:featured_image w="1080" h="720" fit="crop" q="80" }}" alt="{{ featured_image:alt ?? title }}">
                                {{ else }}
                                    <div></div>
                                {{ /if }}

                                <div>
                                    <div>
                                        <time datetime="{{ date format='c' }}">
                                            {{ date format="j M Y" }}
                                        </time>
                                        <h2>{{ title }}</h2>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </article>
                {{ /collection:news }}
            </div>

            <!-- Compact Calendar -->
            <div>
                <div>
                    <h3>Kommande evenemang</h3>
                    <a href="/kalender">Visa alla</a>
                </div>

                <div>
                    {{ autocal:events eventdays="5" group_by_date="true" }}
                        {{ if is_multiple }}
                            <!-- Multiple events on same day - show as stack -->
                            <a href="{{ calendar_url }}">
                                <div>
                                    <span>{{ date_formatted format="j" }}</span>
                                    <span>{{ date_formatted format="M" }}</span>
                                </div>
                                <div>
                                    <h4>{{ display_title }}</h4>
                                    <div>
                                        <span>{{ display_time }}</span>
                                        <span>{{ display_location }}</span>
                                    </div>
                                </div>
                            </a>
                        {{ else }}
                            <!-- Single event - show normally -->
                            <a href="{{ single_event:event_url }}">
                                <div>
                                    <span>{{ date_formatted format="j" }}</span>
                                    <span>{{ date_formatted format="M" }}</span>
                                </div>
                                <div>
                                    <h4>{{ single_event:title }}</h4>
                                    <div>
                                        <span>{{ single_event:start_date format="H:i" }}</span>
                                        {{ if single_event:location }}
                                            <span>{{ single_event:location | truncate:25 }}</span>
                                        {{ /if }}
                                    </div>
                                </div>
                            </a>
                        {{ /if }}
                    {{ /autocal:events }}
                </div>

                <div>
                    <a href="/kalender">
                        Se fullständig kalender →
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Latest News Carousel -->
<section>
    <div>
        <div>
            <h3>Senaste nyheterna</h3>
            <div>
                <button aria-label="Föregående nyheter">
                    ←
                </button>
                <button aria-label="Nästa nyheter">
                    →
                </button>
            </div>
        </div>

        <div>
            <div>
                {{ collection:news offset="1" limit="8" }}
                    <article>
                        <a href="{{ url }}">
                            <div>
                                {{ if featured_image }}
                                    <img src="{{ glide:featured_image preset="newslist" }}" alt="{{ featured_image:alt ?? title }}">
                                {{ else }}
                                    <div>
                                        [Bild saknas]
                                    </div>
                                {{ /if }}
                            </div>

                            <div>
                                <time datetime="{{ date format='c' }}">
                                    {{ date format="j M Y" }}
                                </time>
                                <h4>{{ title }}</h4>
                                {{ if lead }}
                                    <p>{{ lead | truncate:100 }}</p>
                                {{ /if }}
                            </div>
                        </a>
                    </article>
                {{ /collection:news }}

                <!-- News Archive CTA Card -->
                <a href="/nyheter" aria-label="Gå till nyhetsarkivet">
                    <div>
                        <div>
                            [Arkiv]
                        </div>
                    </div>

                    <div>
                        <span>Nyhetsarkiv</span>
                        <h4>
                            <span>Läs alla nyheter</span>
                        </h4>
                        <p>
                            Utforska vårt kompletta nyhetsarkiv med alla artiklar, rapporter och uppdateringar från OK Tyr.
                        </p>
                        <div>
                            <span>Visa arkiv →</span>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</section>

<section>
    {{ content }}
</section>
